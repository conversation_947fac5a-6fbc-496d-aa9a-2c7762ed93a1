<template>
  <div class="enterprise-resource-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Building2 class="title-icon" />
          企业资源管理
        </h1>
        <p class="page-description">管理企业共享的主机配置、密钥链和代码片段，以及同步、监控、安全功能</p>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          :disabled="loading"
          @click="toggleCompactMode"
        >
          <Eye class="btn-icon" />
          {{ compactMode ? '展开视图' : '紧凑视图' }}
        </button>
        <button
          class="btn btn-secondary"
          :disabled="loading"
          @click="refreshResources"
        >
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新
        </button>
        <button
          class="btn btn-primary"
          :disabled="!hasPermission('enterprise_sync', 'execute')"
          @click="showSyncDialog = true"
        >
          <RotateCw class="btn-icon" />
          立即同步
        </button>
      </div>
    </div>

    <!-- 功能模块导航 -->
    <div class="resource-content-section">
      <div class="function-modules-section">
        <h2 class="section-title">企业功能模块</h2>
        <div class="function-nav-grid">
          <div
            class="function-nav-card"
            @click="navigateToResources"
          >
            <div class="nav-card-icon">
              <Database class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">资源管理</h3>
              <p class="nav-card-desc">管理企业主机、密钥链和代码片段</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToSync"
          >
            <div class="nav-card-icon">
              <RefreshCw class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">同步设置</h3>
              <p class="nav-card-desc">配置同步策略、频率和后端存储设置</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToMonitor"
          >
            <div class="nav-card-icon">
              <Activity class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">监控仪表板</h3>
              <p class="nav-card-desc">查看系统状态和性能指标</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToSecurity"
          >
            <div class="nav-card-icon">
              <Shield class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">安全管理</h3>
              <p class="nav-card-desc">权限配置和安全审计</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToSMB"
          >
            <div class="nav-card-icon">
              <HardDrive class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">SMB管理工具</h3>
              <p class="nav-card-desc">文件扫描、共享和远程拷贝功能</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToAssetManagement"
          >
            <div class="nav-card-icon">
              <Package class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">固定资产管理</h3>
              <p class="nav-card-desc">管理企业固定资产、设备清单和资产状态</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToRoomManagement"
          >
            <div class="nav-card-icon">
              <Building class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">机房管理</h3>
              <p class="nav-card-desc">机房设备监控、环境管理和空间规划</p>
            </div>
          </div>

          <!-- 网络基础设施监控模块 -->
          <div
            class="function-nav-card"
            @click="navigateToNetworkMonitor"
          >
            <div class="nav-card-icon">
              <Network class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">网络基础设施监控</h3>
              <p class="nav-card-desc">IP地址池、DHCP、DNS服务和网络设备监控</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToNetworkDevices"
          >
            <div class="nav-card-icon">
              <Router class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">网络设备监控</h3>
              <p class="nav-card-desc">交换机、路由器、防火墙设备状态监控</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToWifiMonitor"
          >
            <div class="nav-card-icon">
              <Wifi class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">无线网络监控</h3>
              <p class="nav-card-desc">WiFi覆盖、AP设备和无线信号质量监控</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToNetworkPerformance"
          >
            <div class="nav-card-icon">
              <Activity class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">网络性能监控</h3>
              <p class="nav-card-desc">带宽使用率、延迟监控和网络质量分析</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToNetworkSecurity"
          >
            <div class="nav-card-icon">
              <Shield class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">网络安全监控</h3>
              <p class="nav-card-desc">流量分析、异常检测和安全事件监控</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToApplicationMonitor"
          >
            <div class="nav-card-icon">
              <Globe class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">应用层监控</h3>
              <p class="nav-card-desc">Web服务、邮件服务和数据库连接监控</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToNetworkAssets"
          >
            <div class="nav-card-icon">
              <Server class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">网络资产管理</h3>
              <p class="nav-card-desc">网络设备清单、配置管理和生命周期跟踪</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToNetworkAnalytics"
          >
            <div class="nav-card-icon">
              <Monitor class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">网络数据分析</h3>
              <p class="nav-card-desc">容量规划、趋势分析和网络优化建议</p>
            </div>
          </div>

          <!-- 新增功能模块 -->
          <div
            class="function-nav-card"
            @click="navigateToNetworkTopology"
          >
            <div class="nav-card-icon">
              <Network class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">网络拓扑可视化管理</h3>
              <p class="nav-card-desc">交互式网络拓扑图、设备连接关系和路径分析</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToAutomation"
          >
            <div class="nav-card-icon">
              <Bot class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">自动化运维管理</h3>
              <p class="nav-card-desc">自动化脚本管理、任务调度和批量操作工具</p>
            </div>
          </div>

          <div
            class="function-nav-card"
            @click="navigateToConfiguration"
          >
            <div class="nav-card-icon">
              <Settings class="icon" />
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">配置管理和变更控制</h3>
              <p class="nav-card-desc">设备配置备份、版本管理和变更审批流程</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 快速操作区域 -->
      <div class="quick-actions-section">
        <h3 class="section-subtitle">快速操作</h3>
        <div class="quick-actions-grid">
          <button
            class="quick-action-btn"
            @click="createNewHost"
          >
            <Plus class="action-icon" />
            <span>新建主机配置</span>
          </button>
          <button
            class="quick-action-btn"
            @click="importResources"
          >
            <Upload class="action-icon" />
            <span>导入资源</span>
          </button>
          <button
            class="quick-action-btn"
            @click="exportResources"
          >
            <Download class="action-icon" />
            <span>导出资源</span>
          </button>
          <button
            class="quick-action-btn"
            @click="syncResources"
          >
            <RefreshCw class="action-icon" />
            <span>同步资源</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 同步对话框 -->
    <div
      v-if="showSyncDialog"
      class="modal-overlay"
      @click="showSyncDialog = false"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">同步企业资源</h2>
          <button
            class="modal-close"
            title="关闭"
            @click="showSyncDialog = false"
          >
            <X class="close-icon" />
          </button>
        </div>

        <div class="modal-body">
          <div class="sync-options">
            <div class="option-group">
              <div class="option-label">同步类型</div>
              <div class="radio-group">
                <label class="radio-item">
                  <input
                    v-model="syncOptions.type"
                    type="radio"
                    value="incremental"
                  />
                  <span class="radio-text">增量同步</span>
                </label>
                <label class="radio-item">
                  <input
                    v-model="syncOptions.type"
                    type="radio"
                    value="full"
                  />
                  <span class="radio-text">完全同步</span>
                </label>
              </div>
            </div>

            <div class="option-group">
              <div class="option-label">目标后端</div>
              <div class="checkbox-group">
                <label
                  v-for="backend in availableBackends"
                  :key="backend.id"
                  class="checkbox-option"
                >
                  <input
                    v-model="syncOptions.targetBackends"
                    type="checkbox"
                    :value="backend.id"
                  />
                  <span class="checkbox-text">{{ backend.name }}</span>
                  <span
                    class="backend-status"
                    :class="backend.health_status"
                  >
                    {{ backend.health_status === 'healthy' ? '健康' : '异常' }}
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="showSyncDialog = false"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            :disabled="syncing"
            @click="startSync"
          >
            <Loader2
              v-if="syncing"
              class="btn-icon animate-spin"
            />
            <RotateCw
              v-else
              class="btn-icon"
            />
            {{ syncing ? '同步中...' : '开始同步' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 资源详情对话框 -->
    <div
      v-if="selectedResource"
      class="modal-overlay"
      @click="selectedResource = null"
    >
      <div
        class="modal-content resource-details"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">{{ selectedResource.name }}</h2>
          <button
            class="modal-close"
            @click="selectedResource = null"
          >
            <X class="close-icon" />
          </button>
        </div>

        <div class="modal-body">
          <div class="details-grid">
            <div class="detail-item">
              <div class="detail-label">资源类型</div>
              <span class="detail-value">{{ getResourceTypeLabel(selectedResource.resource_type) }}</span>
            </div>

            <div class="detail-item">
              <div class="detail-label">环境</div>
              <span class="detail-value">{{ getEnvironmentLabel(selectedResource.environment) }}</span>
            </div>

            <div class="detail-item">
              <div class="detail-label">版本</div>
              <span class="detail-value">v{{ selectedResource.version }}</span>
            </div>

            <div class="detail-item">
              <div class="detail-label">创建时间</div>
              <span class="detail-value">{{ formatDate(selectedResource.created_at) }}</span>
            </div>

            <div class="detail-item">
              <div class="detail-label">更新时间</div>
              <span class="detail-value">{{ formatDate(selectedResource.updated_at) }}</span>
            </div>

            <div class="detail-item">
              <div class="detail-label">标签</div>
              <div class="tags-list">
                <span
                  v-for="tag in selectedResource.tags"
                  :key="tag"
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>

            <div class="detail-item full-width">
              <div class="detail-label">配置数据</div>
              <pre class="config-data">{{ JSON.stringify(selectedResource.config_data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  Building2,
  RefreshCw,
  RotateCw,
  X,
  Activity,
  Shield,
  Eye,
  Database,
  Key,
  Code,
  Users,
  Plus,
  Upload,
  Download,
  Loader2,
  HardDrive,
  Package,
  Building,
  Network,
  Router,
  Wifi,
  Globe,
  Server,
  Monitor,
  Bot,
  Settings
} from 'lucide-vue-next'
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'

// 类型定义
interface EnterpriseResource {
  id: string
  backend_id: string
  resource_type: 'host' | 'keychain' | 'snippet'
  name: string
  config_data: any
  environment: 'production' | 'staging' | 'development'
  tags: string[]
  version: number
  checksum?: string
  created_at: string
  updated_at: string
}

interface Backend {
  id: string
  name: string
  type: string
  health_status: 'healthy' | 'unhealthy' | 'unknown'
  enabled: boolean
}

// 响应式数据
const router = useRouter()
const loading = ref(false)
const error = ref('')
const resources = ref<EnterpriseResource[]>([])
const availableBackends = ref<Backend[]>([])
const showSyncDialog = ref(false)
const selectedResource = ref<EnterpriseResource | null>(null)
const syncing = ref(false)
const compactMode = ref(false)

// 筛选条件
const filters = ref({
  resourceType: '',
  environment: '',
  tags: '',
  searchTerm: ''
})

// 分页
const currentPage = ref(1)
const pageSize = 12

// 同步选项
const syncOptions = ref({
  type: 'incremental',
  targetBackends: [] as string[]
})

// 计算属性
const filteredResources = computed(() => {
  let filtered = resources.value

  // 按资源类型筛选
  if (filters.value.resourceType) {
    filtered = filtered.filter((r) => r.resource_type === filters.value.resourceType)
  }

  // 按环境筛选
  if (filters.value.environment) {
    filtered = filtered.filter((r) => r.environment === filters.value.environment)
  }

  // 按标签筛选
  if (filters.value.tags) {
    const filterTags = filters.value.tags.split(',').map((t) => t.trim().toLowerCase())
    filtered = filtered.filter((r) => filterTags.some((tag) => r.tags.some((resourceTag) => resourceTag.toLowerCase().includes(tag))))
  }

  // 按搜索词筛选
  if (filters.value.searchTerm) {
    const searchTerm = filters.value.searchTerm.toLowerCase()
    filtered = filtered.filter((r) => r.name.toLowerCase().includes(searchTerm) || JSON.stringify(r.config_data).toLowerCase().includes(searchTerm))
  }

  // 分页
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filtered.slice(start, end)
})

const totalPages = computed(() => {
  let filtered = resources.value

  if (filters.value.resourceType) {
    filtered = filtered.filter((r) => r.resource_type === filters.value.resourceType)
  }
  if (filters.value.environment) {
    filtered = filtered.filter((r) => r.environment === filters.value.environment)
  }
  if (filters.value.tags) {
    const filterTags = filters.value.tags.split(',').map((t) => t.trim().toLowerCase())
    filtered = filtered.filter((r) => filterTags.some((tag) => r.tags.some((resourceTag) => resourceTag.toLowerCase().includes(tag))))
  }
  if (filters.value.searchTerm) {
    const searchTerm = filters.value.searchTerm.toLowerCase()
    filtered = filtered.filter((r) => r.name.toLowerCase().includes(searchTerm) || JSON.stringify(r.config_data).toLowerCase().includes(searchTerm))
  }

  return Math.ceil(filtered.length / pageSize)
})

// 方法
const hasPermission = (resource: string, action: string): boolean => {
  // TODO: 实现权限检查逻辑
  return true
}

const getResourceTypeLabel = (type: string): string => {
  const labels = {
    host: '主机配置',
    keychain: '密钥链',
    snippet: '代码片段'
  }
  return labels[type as keyof typeof labels] || type
}

const getEnvironmentLabel = (env: string): string => {
  const labels = {
    production: '生产环境',
    staging: '预发布环境',
    development: '开发环境'
  }
  return labels[env as keyof typeof labels] || env
}

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateString
  }
}

// 快速操作方法
const createNewHost = () => {
  // 创建新主机配置的逻辑
  console.log('创建新主机配置')
}

const importResources = () => {
  // 导入资源的逻辑
  console.log('导入资源')
}

const exportResources = () => {
  console.log('导出资源')
}

const syncResources = () => {
  showSyncDialog.value = true
}

// 功能模块导航方法 - 跳转到具体的资源管理功能页面
const navigateToResources = () => {
  router.push('/enterprise/resources')
}

const navigateToSync = () => {
  router.push('/sync/config')
}

const navigateToMonitor = () => {
  router.push('/sync/monitor')
}

const navigateToSecurity = () => {
  router.push('/security/permissions')
}

const navigateToSMB = () => {
  router.push('/smb/management')
}

const navigateToAssetManagement = () => {
  router.push('/enterprise/asset-management')
}

const navigateToRoomManagement = () => {
  router.push('/enterprise/room-management')
}

const navigateToNetworkMonitor = () => {
  router.push('/enterprise/network/infrastructure')
}

const navigateToNetworkDevices = () => {
  router.push('/enterprise/network/devices')
}

const navigateToWifiMonitor = () => {
  router.push('/enterprise/network/wifi')
}

const navigateToNetworkPerformance = () => {
  router.push('/enterprise/network/performance')
}

const navigateToNetworkSecurity = () => {
  router.push('/enterprise/network/security')
}

const navigateToApplicationMonitor = () => {
  router.push('/enterprise/network/applications')
}

const navigateToNetworkAssets = () => {
  router.push('/enterprise/network/assets')
}

const navigateToNetworkAnalytics = () => {
  router.push('/enterprise/network/analytics')
}

const navigateToNetworkTopology = () => {
  router.push('/enterprise/network/topology')
}

const navigateToAutomation = () => {
  router.push('/enterprise/automation')
}

const navigateToConfiguration = () => {
  router.push('/enterprise/config')
}

const toggleCompactMode = () => {
  compactMode.value = !compactMode.value
}

const refreshResources = async () => {
  loading.value = true
  error.value = ''

  try {
    // TODO: 调用API获取企业资源
    // const response = await enterpriseApi.getResources()
    // resources.value = response.data

    // 模拟数据
    await new Promise((resolve) => setTimeout(resolve, 1000))
    resources.value = [
      {
        id: '1',
        backend_id: 'backend-1',
        resource_type: 'host',
        name: 'Web服务器-01',
        config_data: {
          ip: '*************',
          port: 22,
          username: 'admin'
        },
        environment: 'production',
        tags: ['web', 'nginx', 'production'],
        version: 1,
        created_at: '2025-01-15T10:00:00Z',
        updated_at: '2025-01-15T10:00:00Z'
      },
      {
        id: '2',
        backend_id: 'backend-1',
        resource_type: 'keychain',
        name: 'SSH密钥-生产环境',
        config_data: {
          private_key: '-----BEGIN RSA PRIVATE KEY-----\n...',
          public_key: 'ssh-rsa AAAAB3...'
        },
        environment: 'production',
        tags: ['ssh', 'production', 'security'],
        version: 2,
        created_at: '2025-01-15T09:00:00Z',
        updated_at: '2025-01-15T11:00:00Z'
      }
    ]
  } catch (err: any) {
    error.value = err.message || '加载企业资源失败'
  } finally {
    loading.value = false
  }
}

const loadBackends = async () => {
  try {
    // TODO: 调用API获取后端列表
    // const response = await enterpriseApi.getBackends()
    // availableBackends.value = response.data

    // 模拟数据
    availableBackends.value = [
      {
        id: 'backend-1',
        name: '主数据库',
        type: 'database',
        health_status: 'healthy',
        enabled: true
      },
      {
        id: 'backend-2',
        name: 'SMB共享',
        type: 'smb',
        health_status: 'healthy',
        enabled: true
      }
    ]
  } catch (err) {
    console.warn('加载后端列表失败:', err)
  }
}

const viewResourceDetails = (resource: EnterpriseResource) => {
  selectedResource.value = resource
}

const startSync = async () => {
  syncing.value = true

  try {
    // TODO: 调用同步API
    // const response = await enterpriseApi.startSync({
    //   type: syncOptions.value.type,
    //   target_backends: syncOptions.value.targetBackends
    // })

    // 模拟同步过程
    await new Promise((resolve) => setTimeout(resolve, 2000))

    showSyncDialog.value = false
    // 刷新资源列表
    await refreshResources()
  } catch (err: any) {
    error.value = err.message || '同步失败'
  } finally {
    syncing.value = false
  }
}

// 监听筛选条件变化，重置分页
watch(
  filters,
  () => {
    currentPage.value = 1
  },
  { deep: true }
)

// 组件挂载时加载数据
onMounted(() => {
  refreshResources()
  loadBackends()
})
</script>

<style lang="less" scoped>
.enterprise-resource-management {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #333333);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 0;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px 24px;
  background: var(--bg-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
  -webkit-app-region: drag;
}

.page-header .header-actions {
  -webkit-app-region: no-drag;
}

.page-header .header-actions button {
  -webkit-app-region: no-drag;
}

/* 页面头部响应式调整 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px 20px;
  }

  .header-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .header-actions .btn {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 12px 16px;
  }

  .resource-content-section {
    padding: 12px 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }
}

/* 资源内容区域 */
.resource-content-section {
  flex: 1;
  padding: 16px 24px;
  background: var(--bg-color, #ffffff);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color, #333333);
  margin-bottom: 12px;
}

/* 资源统计卡片样式 */
.resource-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .resource-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }
}

@media (max-width: 768px) {
  .resource-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .resource-stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

.stat-card {
  background: var(--bg-color-secondary, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: var(--hover-bg-color, #f8fafc);
  border-color: var(--primary-color, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color, #3b82f6), #2563eb);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon .icon {
  width: 20px;
  height: 20px;
}

.stat-content {
  flex: 1;
}

.stat-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color-secondary, #6b7280);
  margin: 0 0 0.25rem 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color, #333333);
  margin: 0 0 0.25rem 0;
}

.stat-desc {
  font-size: 0.8rem;
  color: var(--text-color-secondary, #6b7280);
  margin: 0;
}

/* 功能模块导航样式 */
.function-modules-section {
  margin-top: 2rem;
}

.section-subtitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color, #333333);
  margin: 0 0 1rem 0;
}

.function-nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 2rem;
  width: 100%;
}

/* 功能模块网格响应式调整 */
@media (max-width: 1400px) {
  .function-nav-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 1024px) {
  .function-nav-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .function-nav-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .function-nav-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.function-nav-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: var(--bg-color-secondary, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.function-nav-card:hover {
  background: var(--hover-bg-color, #f8fafc);
  border-color: var(--primary-color, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.nav-card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color, #3b82f6), #2563eb);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-card-icon .icon {
  width: 24px;
  height: 24px;
  color: white;
}

.nav-card-content {
  flex: 1;
  min-width: 0;
}

.nav-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333333);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.nav-card-desc {
  font-size: 13px;
  color: var(--text-color-secondary, #6b7280);
  margin: 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 快速操作区域样式 */
.quick-actions-section {
  margin-top: 2rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
  width: 100%;
}

/* 快速操作网格响应式调整 */
@media (max-width: 1024px) {
  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

.quick-action-btn {
  background: var(--bg-color-secondary, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color, #333333);
}

.quick-action-btn:hover {
  background: var(--primary-color, #3b82f6);
  border-color: var(--primary-color, #3b82f6);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

/* 紧凑模式样式 */
.resource-content-section.compact-mode {
  padding: 12px 24px;
}

.resource-content-section.compact-mode .section-title {
  font-size: 16px;
  margin-bottom: 8px;
}

.resource-content-section.compact-mode .resource-stats-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.resource-content-section.compact-mode .stat-card {
  padding: 12px;
  gap: 10px;
}

.resource-content-section.compact-mode .stat-icon {
  width: 32px;
  height: 32px;
}

.resource-content-section.compact-mode .stat-icon .icon {
  width: 16px;
  height: 16px;
}

.resource-content-section.compact-mode .stat-title {
  font-size: 13px;
  margin-bottom: 1px;
}

.resource-content-section.compact-mode .stat-desc {
  font-size: 11px;
  line-height: 1.2;
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color, #333333);
}

.title-icon {
  width: 28px;
  height: 28px;
  color: var(--primary-color, #3b82f6);
}

.page-description {
  margin: 0;
  color: var(--text-color-secondary, #6b7280);
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

/* 筛选器样式 */
.filters {
  display: flex;
  gap: 12px;
  margin: 16px 24px;
  margin-bottom: 12px;
  padding: 16px;
  background: var(--bg-color-secondary, #f9fafb);
  border-radius: 8px;
  border: 1px solid var(--border-color, #e5e7eb);
  flex-wrap: wrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 160px;
  flex: 1;
}

.filter-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color-secondary, #6b7280);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.filter-input,
.filter-select {
  padding: 10px 14px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 8px;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  font-size: 14px;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--primary-color, #3b82f6);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:hover {
    border-color: var(--primary-color, #3b82f6);
  }
}

/* 资源列表样式 */
.resource-list {
  flex: 1;
  overflow: auto;
  padding: 0 24px 24px;
  min-height: 400px; /* 确保有足够的最小高度 */
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 16px;
  text-align: center;
}

.loading-icon,
.error-icon,
.empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: var(--text-color-secondary, #6b7280);
}

.loading-message,
.error-message,
.empty-message {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color, #333333);
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  color: var(--text-color-secondary, #6b7280);
  margin: 0;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.resource-card {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &:hover {
    border-color: var(--primary-color, #3b82f6);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.resource-info {
  flex: 1;
}

.resource-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333333);
  margin: 0 0 4px 0;
}

.resource-type {
  font-size: 14px;
  color: var(--text-color-secondary, #6b7280);
}

.resource-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.status-active {
    background: #dcfce7;
    color: #166534;
  }
}

.resource-content {
  margin-bottom: 16px;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.tag {
  padding: 2px 8px;
  background: var(--bg-color-secondary, #f3f4f6);
  color: var(--text-color-secondary, #6b7280);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.resource-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.meta-item {
  display: flex;
  gap: 4px;
  color: var(--text-color-secondary, #6b7280);
}

.meta-label {
  font-weight: 500;
}

.meta-value {
  color: var(--text-color, #333333);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.update-time {
  font-size: 12px;
  color: var(--text-color-secondary, #6b7280);
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: var(--bg-color-secondary, #f3f4f6);
  color: var(--text-color-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--primary-color, #3b82f6);
    color: white;
  }
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  color: #ffffff !important;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
  }
}

.btn-secondary {
  background: #ffffff !important;
  color: #374151 !important;
  border: 2px solid #d1d5db !important;
  font-weight: 600;

  &:hover:not(:disabled) {
    background: #f9fafb !important;
    border-color: #3b82f6 !important;
    color: #1f2937 !important;
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 分页样式 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  margin-top: auto;
}

.pagination-btn {
  padding: 10px 16px;
  border: 1px solid var(--border-color, #d1d5db);
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    background: var(--hover-bg-color, #f9fafb);
    border-color: var(--primary-color, #3b82f6);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.pagination-info {
  margin: 0 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-secondary, #6b7280);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(6px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #ffffff !important;
  border-radius: 12px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  border: 2px solid #e5e7eb;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: auto;

  &.large {
    max-width: 800px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 2px solid #e5e7eb;
  background: #ffffff !important;
}

.modal-title {
  font-size: 18px;
  font-weight: 700;
  color: #111827 !important;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #ffffff;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.modal-close:hover {
  background: #f3f4f6;
  color: #111827;
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.close-icon {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.modal-body {
  padding: 24px;
  background: #ffffff !important;
  color: #111827 !important;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 2px solid #e5e7eb;
  background: #ffffff !important;
}

/* 同步选项样式 */
.sync-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color, #333333);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-text {
  font-size: 14px;
  color: var(--text-color, #333333);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-text {
  font-size: 14px;
  color: var(--text-color, #333333);
}

.backend-status {
  margin-left: auto;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;

  &.healthy {
    background: #dcfce7;
    color: #166534;
  }

  &.unhealthy {
    background: #fef2f2;
    color: #dc2626;
  }
}

/* 详情网格样式 */
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;

  &.full-width {
    grid-column: 1 / -1;
  }
}

.detail-label {
  font-size: 12px;
  font-weight: 700;
  color: #6b7280 !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  color: #111827 !important;
  font-weight: 600;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.config-data {
  background: #f1f5f9 !important;
  border: 2px solid #cbd5e1 !important;
  border-radius: 8px;
  padding: 20px;
  font-size: 14px;
  color: #0f172a !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
  line-height: 1.6;
}

/* 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-color-hover);
}

.btn-primary:disabled {
  background: var(--border-color);
  cursor: not-allowed;
}

.btn-secondary {
  background: var(--bg-color-secondary);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--hover-bg-color);
}

.btn-secondary:disabled {
  background: var(--bg-color-secondary);
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.filters-section {
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 24px;
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.filter-select,
.filter-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 14px;
}

.filter-select:focus,
.filter-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-light);
}

.search-group {
  flex: 1;
  min-width: 200px;
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: var(--text-color-secondary);
}

.search-input {
  padding-left: 40px;
  padding-right: 12px;
  padding-top: 8px;
  padding-bottom: 8px;
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-light);
}

.resources-section {
  min-height: 400px;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 16px;
  text-align: center;
}

.loading-icon,
.error-icon,
.empty-icon {
  width: 48px;
  height: 48px;
  color: var(--text-color-secondary);
  margin-bottom: 16px;
}

.error-message,
.empty-message {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8px;
}

.empty-hint {
  color: var(--text-color-secondary);
}

.resources-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 768px) {
  .resources-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .resources-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.resource-card {
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 24px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.resource-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.resource-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.resource-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-color-light);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resource-icon svg {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
}

.resource-info {
  flex: 1;
}

.resource-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.resource-type {
  font-size: 14px;
  color: var(--text-color-secondary);
}

.resource-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
}

.status-production {
  background: #fee2e2;
  color: #991b1b;
}

.status-staging {
  background: #fef3c7;
  color: #92400e;
}

.status-development {
  background: #dcfce7;
  color: #166534;
}

.resource-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 4px 8px;
  font-size: 12px;
  background: var(--bg-color);
  color: var(--text-color-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.resource-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-icon {
  width: 16px;
  height: 16px;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn:hover {
  background: var(--hover-bg-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-icon {
  width: 16px;
  height: 16px;
}

.pagination-info {
  font-size: 14px;
  color: var(--text-color-secondary);
}

/* 移除重复的样式定义，使用上面已定义的样式 */

.sync-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-option,
.checkbox-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-option:hover,
.checkbox-option:hover {
  background: var(--hover-bg-color);
}

.radio-text,
.checkbox-text {
  flex: 1;
  font-size: 14px;
  color: var(--text-color);
}

.backend-status {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
}

.backend-status.healthy {
  background: #dcfce7;
  color: #166534;
}

.backend-status.unhealthy {
  background: #fee2e2;
  color: #991b1b;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 768px) {
  .details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.detail-value {
  font-size: 14px;
  color: var(--text-color);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.config-data {
  background: #f1f5f9 !important;
  border: 2px solid #cbd5e1 !important;
  border-radius: 8px;
  padding: 20px;
  font-size: 14px;
  color: #0f172a !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  overflow-x: auto;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
  line-height: 1.6;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
