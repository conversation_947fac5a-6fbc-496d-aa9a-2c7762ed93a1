<template>
  <div class="room-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="btn btn-back"
          title="返回企业资源管理"
          @click="goBack"
        >
          <ArrowLeft class="btn-icon" />
          <span class="btn-text">返回</span>
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Building class="title-icon" />
            机房管理中心
          </h1>
          <p class="page-description">统一管理数据中心机房资源、环境监控和设备状态</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          @click="refreshData"
        >
          <RefreshCw class="btn-icon" />
          刷新
        </button>
        <button
          class="btn btn-primary"
          @click="showAddRoomDialog = true"
        >
          <Plus class="btn-icon" />
          新增机房
        </button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card success">
          <div class="card-header">
            <div class="card-icon">
              <CheckCircle class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator positive">+5%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ normalRooms }}</div>
            <div class="metric-label">运行机房</div>
            <div class="metric-description">当前运行状态良好</div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="card-header">
            <div class="card-icon">
              <AlertTriangle class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator negative">-2%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ warningRooms }}</div>
            <div class="metric-label">告警机房</div>
            <div class="metric-description">需要关注温湿度</div>
          </div>
        </div>

        <div class="stat-card danger">
          <div class="card-header">
            <div class="card-icon">
              <XCircle class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator neutral">0%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ criticalRooms }}</div>
            <div class="metric-label">故障机房</div>
            <div class="metric-description">设备离线或异常</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="card-header">
            <div class="card-icon">
              <Activity class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator positive">+12%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ totalRacks }}</div>
            <div class="metric-label">机柜总数</div>
            <div class="metric-description">设备承载能力</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar-section">
      <div class="toolbar-left">
        <div class="search-box">
          <Search class="search-icon" />
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索机房名称或位置..."
            class="search-input"
          />
        </div>
        <div class="filter-tabs">
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'all' }"
            @click="statusFilter = 'all'"
          >
            全部 ({{ totalRooms }})
          </button>
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'normal' }"
            @click="statusFilter = 'normal'"
          >
            正常 ({{ normalRooms }})
          </button>
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'warning' }"
            @click="statusFilter = 'warning'"
          >
            告警 ({{ warningRooms }})
          </button>
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'critical' }"
            @click="statusFilter = 'critical'"
          >
            故障 ({{ criticalRooms }})
          </button>
        </div>
      </div>
      <div class="toolbar-right">
        <button
          class="btn btn-secondary"
          @click="exportData"
        >
          <Download class="btn-icon" />
          导出
        </button>
        <button
          class="btn btn-secondary"
          @click="refreshData"
        >
          <RefreshCw class="btn-icon" />
          刷新
        </button>
      </div>
    </div>

    <!-- 机房列表区域 -->
    <div class="rooms-section">
      <div class="rooms-grid">
        <div
          v-for="room in filteredRooms"
          :key="room.id"
          class="room-card"
          :class="room.status"
          @click="selectRoom(room)"
        >
          <div class="room-header">
            <div class="room-status">
              <div
                class="status-indicator"
                :class="room.status"
              ></div>
              <span class="status-text">{{ getStatusText(room.status) }}</span>
            </div>
            <div class="room-actions">
              <button
                class="action-btn"
                @click.stop="viewRoomDetails(room)"
                title="查看详情"
              >
                <Eye class="action-icon" />
              </button>
              <button
                class="action-btn"
                @click.stop="editRoom(room)"
                title="编辑"
              >
                <Edit class="action-icon" />
              </button>
              <button
                class="action-btn danger"
                @click.stop="deleteRoom(room)"
                title="删除"
              >
                <Trash2 class="action-icon" />
              </button>
            </div>
          </div>

          <div class="room-content">
            <h3 class="room-name">{{ room.name }}</h3>
            <p class="room-location">{{ room.location }}</p>

            <div class="room-metrics">
              <div class="metric-item">
                <span class="metric-label">温度</span>
                <span
                  class="metric-value"
                  :class="getTemperatureClass(room.temperature)"
                >
                  {{ room.temperature }}°C
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">湿度</span>
                <span
                  class="metric-value"
                  :class="getHumidityClass(room.humidity)"
                >
                  {{ room.humidity }}%
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">机柜</span>
                <span class="metric-value"> {{ room.rackCount }} 个 </span>
              </div>
            </div>

            <div class="room-footer">
              <div class="utilization-bar">
                <div class="utilization-label">利用率</div>
                <div class="utilization-progress">
                  <div
                    class="utilization-fill"
                    :style="{ width: room.utilization + '%' }"
                    :class="getUtilizationClass(room.utilization)"
                  ></div>
                </div>
                <div class="utilization-text">{{ room.utilization }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加新机房卡片 -->
        <div
          class="room-card add-room-card"
          @click="showAddRoomDialog = true"
        >
          <div class="add-room-content">
            <Plus class="add-icon" />
            <h3 class="add-title">添加新机房</h3>
            <p class="add-description">点击创建新的机房</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增机房对话框 -->
    <div
      v-if="showAddRoomDialog"
      class="modal-overlay"
      @click="closeAddRoomDialog"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">新增机房</h2>
          <button
            class="modal-close"
            @click="closeAddRoomDialog"
          >
            <X class="close-icon" />
          </button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="saveRoom">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">机房名称</label>
                <input
                  v-model="roomForm.name"
                  type="text"
                  class="form-input"
                  placeholder="请输入机房名称"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">机房位置</label>
                <input
                  v-model="roomForm.location"
                  type="text"
                  class="form-input"
                  placeholder="请输入机房位置"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">机房面积</label>
                <input
                  v-model="roomForm.area"
                  type="number"
                  class="form-input"
                  placeholder="请输入机房面积(平方米)"
                  step="0.1"
                />
              </div>
              <div class="form-group">
                <label class="form-label">机柜数量</label>
                <input
                  v-model="roomForm.rackCount"
                  type="number"
                  class="form-input"
                  placeholder="请输入机柜数量"
                  min="1"
                />
              </div>
            </div>
            <div class="form-group full-width">
              <label class="form-label">备注</label>
              <textarea
                v-model="roomForm.description"
                class="form-textarea"
                placeholder="请输入备注信息"
                rows="3"
              ></textarea>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="closeAddRoomDialog"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            :disabled="saving"
            @click="saveRoom"
          >
            <Loader2
              v-if="saving"
              class="btn-icon animate-spin"
            />
            {{ saving ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Building,
  ArrowLeft,
  RefreshCw,
  Plus,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Activity,
  Search,
  Download,
  Eye,
  Edit,
  Trash2,
  X,
  Loader2
} from 'lucide-vue-next'

const router = useRouter()

// 响应式数据
const showAddRoomDialog = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('all')

// 机房表单数据
const roomForm = ref({
  name: '',
  location: '',
  area: 0,
  rackCount: 0,
  description: ''
})

// 模拟机房数据
const rooms = ref([
  {
    id: 1,
    name: '主机房A',
    location: '北京数据中心1楼',
    area: 500,
    rackCount: 50,
    status: 'normal',
    temperature: 22,
    humidity: 45,
    utilization: 75
  },
  {
    id: 2,
    name: '主机房B',
    location: '北京数据中心2楼',
    area: 400,
    rackCount: 40,
    status: 'warning',
    temperature: 28,
    humidity: 65,
    utilization: 85
  },
  {
    id: 3,
    name: '备用机房',
    location: '北京数据中心3楼',
    area: 300,
    rackCount: 30,
    status: 'critical',
    temperature: 35,
    humidity: 80,
    utilization: 95
  }
])

// 计算属性
const totalRooms = computed(() => rooms.value.length)
const normalRooms = computed(() => rooms.value.filter((room) => room.status === 'normal').length)
const warningRooms = computed(() => rooms.value.filter((room) => room.status === 'warning').length)
const criticalRooms = computed(() => rooms.value.filter((room) => room.status === 'critical').length)
const totalRacks = computed(() => rooms.value.reduce((sum, room) => sum + room.rackCount, 0))

const filteredRooms = computed(() => {
  let filtered = rooms.value

  // 状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter((room) => room.status === statusFilter.value)
  }

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter((room) => room.name.toLowerCase().includes(keyword) || room.location.toLowerCase().includes(keyword))
  }

  return filtered
})

// 方法
const goBack = () => {
  router.push('/enterprise')
}

const refreshData = () => {
  // 刷新数据逻辑
  console.log('刷新机房数据')
}

const exportData = () => {
  // 导出数据逻辑
  console.log('导出机房数据')
}

const closeAddRoomDialog = () => {
  showAddRoomDialog.value = false
  resetForm()
}

const resetForm = () => {
  roomForm.value = {
    name: '',
    location: '',
    area: 0,
    rackCount: 0,
    description: ''
  }
}

const saveRoom = async () => {
  if (!roomForm.value.name || !roomForm.value.location) {
    alert('请填写必填字段')
    return
  }

  saving.value = true

  try {
    // 模拟保存
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const newRoom = {
      id: Date.now(),
      ...roomForm.value,
      status: 'normal',
      temperature: 22,
      humidity: 45,
      utilization: 0
    }

    rooms.value.push(newRoom)
    closeAddRoomDialog()

    alert('机房创建成功！')
  } catch (error) {
    alert('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const selectRoom = (room: any) => {
  console.log('选择机房:', room)
}

const viewRoomDetails = (room: any) => {
  console.log('查看机房详情:', room)
}

const editRoom = (room: any) => {
  console.log('编辑机房:', room)
}

const deleteRoom = (room: any) => {
  if (confirm(`确定要删除机房 "${room.name}" 吗？`)) {
    const index = rooms.value.findIndex((r) => r.id === room.id)
    if (index > -1) {
      rooms.value.splice(index, 1)
      alert('机房删除成功！')
    }
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    normal: '正常',
    warning: '告警',
    critical: '故障'
  }
  return statusMap[status] || '未知'
}

const getTemperatureClass = (temperature: number) => {
  if (temperature > 30) return 'danger'
  if (temperature > 25) return 'warning'
  return 'normal'
}

const getHumidityClass = (humidity: number) => {
  if (humidity > 70) return 'danger'
  if (humidity > 60) return 'warning'
  return 'normal'
}

const getUtilizationClass = (utilization: number) => {
  if (utilization > 90) return 'danger'
  if (utilization > 80) return 'warning'
  return 'normal'
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
  console.log('机房管理页面已加载')
})
</script>

<style scoped>
.room-management {
  width: 100%;
  height: 100vh;
  background: #f8fafc;
  overflow-y: auto;
  -webkit-app-region: no-drag;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.btn-back {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-back:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  font-size: 28px;
  font-weight: 900;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 3;
}

.title-icon {
  width: 32px;
  height: 32px;
}

.page-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: all 0.2s;
  font-size: 14px;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-primary {
  background: #10b981;
  color: white;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-primary:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 统计卡片区域 */
.stats-section {
  padding: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card.success {
  border-left: 4px solid #10b981;
}

.stat-card.warning {
  border-left: 4px solid #f59e0b;
}

.stat-card.danger {
  border-left: 4px solid #ef4444;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success .card-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.warning .card-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.danger .card-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.card-icon .icon {
  width: 24px;
  height: 24px;
}

.card-meta {
  text-align: right;
}

.trend-indicator {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.trend-indicator.positive {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.trend-indicator.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.trend-indicator.neutral {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.trend-text {
  font-size: 12px;
  color: #6b7280;
  display: block;
  margin-top: 2px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
}

.metric-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.metric-description {
  font-size: 12px;
  color: #9ca3af;
}

/* 工具栏 */
.toolbar-section {
  padding: 0 24px 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.search-box {
  position: relative;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  color: #6b7280;
}

.filter-tab:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-tab.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

/* 机房列表 */
.rooms-section {
  padding: 0 24px 24px 24px;
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.room-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.room-card.normal {
  border-left: 4px solid #10b981;
}

.room-card.warning {
  border-left: 4px solid #f59e0b;
}

.room-card.critical {
  border-left: 4px solid #ef4444;
}

.room-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f4f6;
}

.room-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.normal {
  background: #10b981;
}

.status-indicator.warning {
  background: #f59e0b;
}

.status-indicator.critical {
  background: #ef4444;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.room-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #e5e7eb;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #ef4444;
}

.action-icon {
  width: 14px;
  height: 14px;
}

.room-content {
  padding: 16px;
}

.room-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.room-location {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px 0;
}

.room-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
  display: block;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.metric-value.normal {
  color: #10b981;
}

.metric-value.warning {
  color: #f59e0b;
}

.metric-value.danger {
  color: #ef4444;
}

.room-footer {
  border-top: 1px solid #f3f4f6;
  padding-top: 12px;
}

.utilization-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.utilization-label {
  font-size: 12px;
  color: #6b7280;
  min-width: 40px;
}

.utilization-progress {
  flex: 1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.utilization-fill {
  height: 100%;
  transition: width 0.3s;
}

.utilization-fill.normal {
  background: #10b981;
}

.utilization-fill.warning {
  background: #f59e0b;
}

.utilization-fill.danger {
  background: #ef4444;
}

.utilization-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  min-width: 35px;
  text-align: right;
}

/* 添加机房卡片 */
.add-room-card {
  border: 2px dashed #d1d5db;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.add-room-card:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.add-room-content {
  text-align: center;
  color: #6b7280;
}

.add-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 12px;
  color: #9ca3af;
}

.add-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.add-description {
  font-size: 14px;
  margin: 0;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  -webkit-app-region: no-drag;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 10000;
}

.modal-header {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #e5e7eb;
}

.close-icon {
  width: 16px;
  height: 16px;
}

.modal-body {
  padding: 0 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
  background: white;
  color: #1f2937;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .toolbar-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
    gap: 12px;
  }

  .search-box {
    min-width: auto;
  }

  .filter-tabs {
    flex-wrap: wrap;
  }

  .rooms-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style>
