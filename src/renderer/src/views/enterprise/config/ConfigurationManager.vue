<template>
  <div
    class="configuration-manager"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Settings class="title-icon" />
            配置管理和变更控制
          </h1>
          <p class="page-description">设备配置备份、版本管理和变更审批流程</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button @click="backupConfigs">
          <Download class="btn-icon" />
          备份配置
        </a-button>
        <a-button @click="createChangeRequest">
          <Plus class="btn-icon" />
          创建变更
        </a-button>
        <a-button
          type="primary"
          @click="showBatchDeploy"
        >
          <Upload class="btn-icon" />
          批量部署
        </a-button>
      </div>
    </div>

    <!-- 概览统计 -->
    <div class="overview-stats">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <FileText class="stat-icon" />
              配置文件
            </div>
            <div class="stat-value">{{ configStats.totalConfigs }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">已备份:</span>
              <span class="detail-value backed-up">{{ configStats.backedUpConfigs }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">待备份:</span>
              <span class="detail-value pending">{{ configStats.pendingBackup }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <GitBranch class="stat-icon" />
              版本管理
            </div>
            <div class="stat-value">{{ configStats.totalVersions }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">活跃版本:</span>
              <span class="detail-value active">{{ configStats.activeVersions }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">历史版本:</span>
              <span class="detail-value history">{{ configStats.historicalVersions }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Clock class="stat-icon" />
              变更请求
            </div>
            <div class="stat-value">{{ configStats.totalChanges }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">待审批:</span>
              <span class="detail-value pending">{{ configStats.pendingChanges }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">已完成:</span>
              <span class="detail-value completed">{{ configStats.completedChanges }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Shield class="stat-icon" />
              合规检查
            </div>
            <div class="stat-value">{{ configStats.complianceScore }}%</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">合规:</span>
              <span class="detail-value compliant">{{ configStats.compliantDevices }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">违规:</span>
              <span class="detail-value non-compliant">{{ configStats.nonCompliantDevices }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-tabs
        v-model:active-key="activeTab"
        type="card"
      >
        <!-- 配置备份 -->
        <a-tab-pane
          key="backup"
          tab="配置备份"
        >
          <div class="backup-section">
            <div class="section-header">
              <h2 class="section-title">设备配置备份</h2>
              <div class="section-filters">
                <a-select
                  v-model:value="selectedDeviceType"
                  style="width: 120px"
                >
                  <a-select-option value="all">全部设备</a-select-option>
                  <a-select-option value="router">路由器</a-select-option>
                  <a-select-option value="switch">交换机</a-select-option>
                  <a-select-option value="firewall">防火墙</a-select-option>
                </a-select>
                <a-select
                  v-model:value="selectedBackupStatus"
                  style="width: 100px"
                >
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="success">成功</a-select-option>
                  <a-select-option value="failed">失败</a-select-option>
                  <a-select-option value="pending">待备份</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="backup-table">
              <a-table
                :columns="backupColumns"
                :data-source="filteredBackups"
                :loading="loading"
                :pagination="{ pageSize: 10 }"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'device'">
                    <div class="device-info">
                      <component
                        :is="getDeviceIcon(record.deviceType)"
                        class="device-icon"
                      />
                      <div class="device-details">
                        <div class="device-name">{{ record.deviceName }}</div>
                        <div class="device-ip">{{ record.deviceIp }}</div>
                      </div>
                    </div>
                  </template>
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getBackupStatusColor(record.status)">
                      {{ getBackupStatusLabel(record.status) }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'size'">
                    {{ formatFileSize(record.size) }}
                  </template>
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button
                        size="small"
                        @click="downloadBackup(record)"
                        :disabled="record.status !== 'success'"
                      >
                        <Download class="btn-icon" />
                        下载
                      </a-button>
                      <a-button
                        size="small"
                        @click="viewBackupDiff(record)"
                        :disabled="record.status !== 'success'"
                      >
                        <GitCompare class="btn-icon" />
                        对比
                      </a-button>
                      <a-button
                        size="small"
                        @click="restoreBackup(record)"
                        :disabled="record.status !== 'success'"
                      >
                        <RotateCcw class="btn-icon" />
                        恢复
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 版本管理 -->
        <a-tab-pane
          key="versions"
          tab="版本管理"
        >
          <div class="versions-section">
            <div class="section-header">
              <h2 class="section-title">配置版本管理</h2>
              <a-button @click="createVersion">
                <Plus class="btn-icon" />
                创建版本
              </a-button>
            </div>

            <div class="versions-grid">
              <div
                v-for="version in configVersions"
                :key="version.id"
                class="version-card"
                :class="{ active: version.isActive }"
              >
                <div class="version-header">
                  <div class="version-info">
                    <GitBranch class="version-icon" />
                    <div class="version-details">
                      <h3 class="version-name">{{ version.name }}</h3>
                      <p class="version-description">{{ version.description }}</p>
                    </div>
                  </div>
                  <div class="version-status">
                    <a-tag
                      v-if="version.isActive"
                      color="green"
                      >当前版本</a-tag
                    >
                    <a-tag
                      v-else
                      color="default"
                      >历史版本</a-tag
                    >
                  </div>
                </div>

                <div class="version-metrics">
                  <div class="metric-item">
                    <span class="metric-label">设备数量</span>
                    <span class="metric-value">{{ version.deviceCount }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">创建时间</span>
                    <span class="metric-value">{{ version.createdAt }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">创建者</span>
                    <span class="metric-value">{{ version.createdBy }}</span>
                  </div>
                </div>

                <div class="version-actions">
                  <a-button
                    size="small"
                    @click="viewVersionDetails(version)"
                  >
                    <Eye class="btn-icon" />
                    查看
                  </a-button>
                  <a-button
                    size="small"
                    @click="compareVersions(version)"
                  >
                    <GitCompare class="btn-icon" />
                    对比
                  </a-button>
                  <a-button
                    v-if="!version.isActive"
                    size="small"
                    @click="activateVersion(version)"
                  >
                    <CheckCircle class="btn-icon" />
                    激活
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 变更控制 -->
        <a-tab-pane
          key="changes"
          tab="变更控制"
        >
          <div class="changes-section">
            <div class="section-header">
              <h2 class="section-title">变更请求管理</h2>
              <div class="section-filters">
                <a-select
                  v-model:value="selectedChangeStatus"
                  style="width: 120px"
                >
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="pending">待审批</a-select-option>
                  <a-select-option value="approved">已批准</a-select-option>
                  <a-select-option value="rejected">已拒绝</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="changes-table">
              <a-table
                :columns="changeColumns"
                :data-source="filteredChanges"
                :loading="loading"
                :pagination="{ pageSize: 10 }"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'priority'">
                    <a-tag :color="getPriorityColor(record.priority)">
                      {{ getPriorityLabel(record.priority) }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getChangeStatusColor(record.status)">
                      {{ getChangeStatusLabel(record.status) }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button
                        size="small"
                        @click="viewChangeDetails(record)"
                      >
                        <Eye class="btn-icon" />
                        查看
                      </a-button>
                      <a-button
                        v-if="record.status === 'pending'"
                        size="small"
                        @click="approveChange(record)"
                      >
                        <CheckCircle class="btn-icon" />
                        批准
                      </a-button>
                      <a-button
                        v-if="record.status === 'approved'"
                        size="small"
                        type="primary"
                        @click="executeChange(record)"
                      >
                        <Play class="btn-icon" />
                        执行
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 合规检查 -->
        <a-tab-pane
          key="compliance"
          tab="合规检查"
        >
          <div class="compliance-section">
            <div class="section-header">
              <h2 class="section-title">配置合规检查</h2>
              <a-button @click="runComplianceCheck">
                <Shield class="btn-icon" />
                运行检查
              </a-button>
            </div>

            <div class="compliance-overview">
              <div class="compliance-chart">
                <div class="chart-container">
                  <div class="chart-placeholder">
                    <Shield class="chart-icon" />
                    <h3>合规性概览</h3>
                    <p>{{ configStats.complianceScore }}% 设备合规</p>
                  </div>
                </div>
              </div>

              <div class="compliance-rules">
                <h3>合规规则</h3>
                <div class="rules-list">
                  <div
                    v-for="rule in complianceRules"
                    :key="rule.id"
                    class="rule-item"
                    :class="rule.status"
                  >
                    <div class="rule-info">
                      <div class="rule-name">{{ rule.name }}</div>
                      <div class="rule-description">{{ rule.description }}</div>
                    </div>
                    <div class="rule-status">
                      <a-tag :color="getRuleStatusColor(rule.status)">
                        {{ getRuleStatusLabel(rule.status) }}
                      </a-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'
import {
  ArrowLeft,
  Settings,
  RefreshCw,
  Download,
  Plus,
  Upload,
  FileText,
  GitBranch,
  Clock,
  Shield,
  GitCompare,
  RotateCcw,
  Eye,
  CheckCircle,
  Play,
  Router,
  Network,
  HardDrive
} from 'lucide-vue-next'

const router = useRouter()
const loading = ref(false)
const activeTab = ref('backup')
const selectedDeviceType = ref('all')
const selectedBackupStatus = ref('all')
const selectedChangeStatus = ref('all')

// 配置统计数据
const configStats = reactive({
  totalConfigs: 156,
  backedUpConfigs: 142,
  pendingBackup: 14,
  totalVersions: 23,
  activeVersions: 5,
  historicalVersions: 18,
  totalChanges: 45,
  pendingChanges: 8,
  completedChanges: 37,
  complianceScore: 87,
  compliantDevices: 135,
  nonCompliantDevices: 21
})

// 备份数据
const backups = ref([
  {
    id: 1,
    deviceName: 'Core-Router-01',
    deviceType: 'router',
    deviceIp: '***********',
    status: 'success',
    size: 2048576,
    lastBackup: '2024-01-15 14:30:25',
    version: 'v1.2.3'
  },
  {
    id: 2,
    deviceName: 'Core-Switch-01',
    deviceType: 'switch',
    deviceIp: '***********0',
    status: 'success',
    size: 1536000,
    lastBackup: '2024-01-15 14:25:18',
    version: 'v2.1.0'
  },
  {
    id: 3,
    deviceName: 'Firewall-Main',
    deviceType: 'firewall',
    deviceIp: '*************',
    status: 'failed',
    size: 0,
    lastBackup: '2024-01-15 14:20:45',
    version: 'v3.0.1'
  }
])

// 版本数据
const configVersions = ref([
  {
    id: 1,
    name: 'v2024.01.15',
    description: '生产环境配置版本',
    isActive: true,
    deviceCount: 24,
    createdAt: '2024-01-15 10:00',
    createdBy: '管理员'
  },
  {
    id: 2,
    name: 'v2024.01.10',
    description: '安全策略更新版本',
    isActive: false,
    deviceCount: 22,
    createdAt: '2024-01-10 15:30',
    createdBy: '网络工程师'
  }
])

// 变更请求数据
const changeRequests = ref([
  {
    id: 1,
    title: '更新防火墙规则',
    description: '添加新的安全策略规则',
    priority: 'high',
    status: 'pending',
    requestedBy: '安全工程师',
    requestedAt: '2024-01-15 09:00',
    scheduledAt: '2024-01-16 02:00'
  },
  {
    id: 2,
    title: '路由器固件升级',
    description: '升级核心路由器固件版本',
    priority: 'medium',
    status: 'approved',
    requestedBy: '网络工程师',
    requestedAt: '2024-01-14 16:30',
    scheduledAt: '2024-01-15 23:00'
  }
])

// 合规规则数据
const complianceRules = ref([
  {
    id: 1,
    name: 'SSH密钥强度检查',
    description: '检查SSH密钥是否符合安全标准',
    status: 'passed',
    lastCheck: '2024-01-15 14:00'
  },
  {
    id: 2,
    name: 'SNMP社区字符串检查',
    description: '检查SNMP社区字符串是否为默认值',
    status: 'failed',
    lastCheck: '2024-01-15 14:00'
  },
  {
    id: 3,
    name: '管理员账户检查',
    description: '检查是否存在默认管理员账户',
    status: 'warning',
    lastCheck: '2024-01-15 14:00'
  }
])

// 表格列定义
const backupColumns = [
  { title: '设备', key: 'device' },
  { title: '状态', key: 'status' },
  { title: '文件大小', key: 'size' },
  { title: '最后备份', dataIndex: 'lastBackup', key: 'lastBackup' },
  { title: '版本', dataIndex: 'version', key: 'version' },
  { title: '操作', key: 'actions' }
]

const changeColumns = [
  { title: '变更标题', dataIndex: 'title', key: 'title' },
  { title: '优先级', key: 'priority' },
  { title: '状态', key: 'status' },
  { title: '申请人', dataIndex: 'requestedBy', key: 'requestedBy' },
  { title: '计划时间', dataIndex: 'scheduledAt', key: 'scheduledAt' },
  { title: '操作', key: 'actions' }
]

// 计算属性
const filteredBackups = computed(() => {
  return backups.value.filter((backup) => {
    const typeMatch = selectedDeviceType.value === 'all' || backup.deviceType === selectedDeviceType.value
    const statusMatch = selectedBackupStatus.value === 'all' || backup.status === selectedBackupStatus.value
    return typeMatch && statusMatch
  })
})

const filteredChanges = computed(() => {
  return changeRequests.value.filter((change) => {
    return selectedChangeStatus.value === 'all' || change.status === selectedChangeStatus.value
  })
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const backupConfigs = () => {
  console.log('开始备份配置')
}

const createChangeRequest = () => {
  console.log('创建变更请求')
}

const showBatchDeploy = () => {
  console.log('显示批量部署')
}

const downloadBackup = (backup: any) => {
  console.log('下载备份:', backup)
}

const viewBackupDiff = (backup: any) => {
  console.log('查看备份差异:', backup)
}

const restoreBackup = (backup: any) => {
  console.log('恢复备份:', backup)
}

const createVersion = () => {
  console.log('创建新版本')
}

const viewVersionDetails = (version: any) => {
  console.log('查看版本详情:', version)
}

const compareVersions = (version: any) => {
  console.log('对比版本:', version)
}

const activateVersion = (version: any) => {
  console.log('激活版本:', version)
}

const viewChangeDetails = (change: any) => {
  console.log('查看变更详情:', change)
}

const approveChange = (change: any) => {
  console.log('批准变更:', change)
}

const executeChange = (change: any) => {
  console.log('执行变更:', change)
}

const runComplianceCheck = () => {
  console.log('运行合规检查')
}

const getDeviceIcon = (type: string) => {
  const icons: Record<string, any> = {
    router: Router,
    switch: Network,
    firewall: Shield,
    server: HardDrive
  }
  return icons[type] || HardDrive
}

const getBackupStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    success: 'green',
    failed: 'red',
    pending: 'orange'
  }
  return colors[status] || 'default'
}

const getBackupStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    success: '成功',
    failed: '失败',
    pending: '待备份'
  }
  return labels[status] || status
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityLabel = (priority: string) => {
  const labels: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return labels[priority] || priority
}

const getChangeStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'orange',
    approved: 'blue',
    rejected: 'red',
    completed: 'green'
  }
  return colors[status] || 'default'
}

const getChangeStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return labels[status] || status
}

const getRuleStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    passed: 'green',
    failed: 'red',
    warning: 'orange'
  }
  return colors[status] || 'default'
}

const getRuleStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    passed: '通过',
    failed: '失败',
    warning: '警告'
  }
  return labels[status] || status
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.configuration-manager {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.overview-stats {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-icon {
  width: 16px;
  height: 16px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
}

.stat-details {
  display: flex;
  gap: 16px;
}

.stat-detail {
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #666;
}

.detail-value {
  font-size: 12px;
  font-weight: 600;
}

.detail-value.backed-up,
.detail-value.active,
.detail-value.completed,
.detail-value.compliant {
  color: #52c41a;
}

.detail-value.pending,
.detail-value.history {
  color: #fa8c16;
}

.detail-value.non-compliant {
  color: #ff4d4f;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.backup-section,
.versions-section,
.changes-section,
.compliance-section {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
  font-weight: 600;
}

.section-filters {
  display: flex;
  gap: 12px;
}

.backup-table,
.changes-table {
  background: white;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-icon {
  width: 24px;
  height: 24px;
  color: #1890ff;
}

.device-details {
  display: flex;
  flex-direction: column;
}

.device-name {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

.device-ip {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.versions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.version-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.version-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.version-card.active {
  border-left: 4px solid #52c41a;
  background: #f6ffed;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.version-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.version-icon {
  width: 32px;
  height: 32px;
  color: #1890ff;
  flex-shrink: 0;
}

.version-details {
  flex: 1;
}

.version-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
  font-weight: 600;
}

.version-description {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.version-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.metric-label {
  font-size: 10px;
  color: #666;
}

.metric-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

.version-actions {
  display: flex;
  gap: 8px;
}

.compliance-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.compliance-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #666;
}

.chart-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #1890ff;
}

.compliance-rules h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.rule-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rule-item.passed {
  border-left: 4px solid #52c41a;
}

.rule-item.failed {
  border-left: 4px solid #ff4d4f;
}

.rule-item.warning {
  border-left: 4px solid #fa8c16;
}

.rule-info {
  flex: 1;
}

.rule-name {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
  margin-bottom: 4px;
}

.rule-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 16px;
  height: 16px;
}
</style>
