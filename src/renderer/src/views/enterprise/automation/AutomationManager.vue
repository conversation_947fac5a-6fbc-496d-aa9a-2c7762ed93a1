<template>
  <div
    class="automation-manager"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Bot class="title-icon" />
            自动化运维管理
          </h1>
          <p class="page-description">自动化脚本管理、任务调度和批量操作工具</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button @click="createScript">
          <Plus class="btn-icon" />
          创建脚本
        </a-button>
        <a-button @click="createTask">
          <Calendar class="btn-icon" />
          创建任务
        </a-button>
        <a-button
          type="primary"
          @click="showBatchOperations"
        >
          <Layers class="btn-icon" />
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 概览统计 -->
    <div class="overview-stats">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <FileCode class="stat-icon" />
              脚本总数
            </div>
            <div class="stat-value">{{ automationStats.totalScripts }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">活跃:</span>
              <span class="detail-value active">{{ automationStats.activeScripts }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">停用:</span>
              <span class="detail-value inactive">{{ automationStats.inactiveScripts }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Clock class="stat-icon" />
              调度任务
            </div>
            <div class="stat-value">{{ automationStats.totalTasks }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">运行中:</span>
              <span class="detail-value running">{{ automationStats.runningTasks }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">等待中:</span>
              <span class="detail-value waiting">{{ automationStats.waitingTasks }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Activity class="stat-icon" />
              执行统计
            </div>
            <div class="stat-value">{{ automationStats.todayExecutions }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">成功:</span>
              <span class="detail-value success">{{ automationStats.successfulExecutions }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">失败:</span>
              <span class="detail-value failed">{{ automationStats.failedExecutions }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Zap class="stat-icon" />
              API集成
            </div>
            <div class="stat-value">{{ automationStats.apiIntegrations }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">在线:</span>
              <span class="detail-value online">{{ automationStats.onlineApis }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">离线:</span>
              <span class="detail-value offline">{{ automationStats.offlineApis }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-tabs
        v-model:active-key="activeTab"
        type="card"
      >
        <!-- 脚本管理 -->
        <a-tab-pane
          key="scripts"
          tab="脚本管理"
        >
          <div class="scripts-section">
            <div class="section-header">
              <h2 class="section-title">自动化脚本</h2>
              <div class="section-filters">
                <a-select
                  v-model:value="selectedScriptType"
                  style="width: 120px"
                >
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="shell">Shell脚本</a-select-option>
                  <a-select-option value="python">Python脚本</a-select-option>
                  <a-select-option value="powershell">PowerShell</a-select-option>
                  <a-select-option value="ansible">Ansible</a-select-option>
                </a-select>
                <a-select
                  v-model:value="selectedScriptStatus"
                  style="width: 100px"
                >
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="active">活跃</a-select-option>
                  <a-select-option value="inactive">停用</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="scripts-grid">
              <div
                v-for="script in filteredScripts"
                :key="script.id"
                class="script-card"
                :class="script.status"
              >
                <div class="script-header">
                  <div class="script-info">
                    <component
                      :is="getScriptIcon(script.type)"
                      class="script-icon"
                    />
                    <div class="script-details">
                      <h3 class="script-name">{{ script.name }}</h3>
                      <p class="script-description">{{ script.description }}</p>
                    </div>
                  </div>
                  <div
                    class="script-status"
                    :class="script.status"
                  >
                    <div class="status-dot"></div>
                    <span>{{ getScriptStatusLabel(script.status) }}</span>
                  </div>
                </div>

                <div class="script-metrics">
                  <div class="metric-item">
                    <span class="metric-label">执行次数</span>
                    <span class="metric-value">{{ script.executionCount }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value">{{ script.successRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">最后执行</span>
                    <span class="metric-value">{{ script.lastExecution }}</span>
                  </div>
                </div>

                <div class="script-actions">
                  <a-button
                    size="small"
                    @click="executeScript(script)"
                    :loading="script.executing"
                  >
                    <Play class="btn-icon" />
                    执行
                  </a-button>
                  <a-button
                    size="small"
                    @click="editScript(script)"
                  >
                    <Edit class="btn-icon" />
                    编辑
                  </a-button>
                  <a-button
                    size="small"
                    @click="viewScriptLogs(script)"
                  >
                    <FileText class="btn-icon" />
                    日志
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 任务调度 -->
        <a-tab-pane
          key="tasks"
          tab="任务调度"
        >
          <div class="tasks-section">
            <div class="section-header">
              <h2 class="section-title">调度任务</h2>
              <div class="section-filters">
                <a-select
                  v-model:value="selectedTaskStatus"
                  style="width: 120px"
                >
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="running">运行中</a-select-option>
                  <a-select-option value="waiting">等待中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="failed">失败</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="tasks-table">
              <a-table
                :columns="taskColumns"
                :data-source="filteredTasks"
                :loading="loading"
                :pagination="{ pageSize: 10 }"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getTaskStatusColor(record.status)">
                      {{ getTaskStatusLabel(record.status) }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'schedule'">
                    <div class="schedule-info">
                      <Clock class="schedule-icon" />
                      <span>{{ record.schedule }}</span>
                    </div>
                  </template>
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button
                        size="small"
                        @click="runTask(record)"
                        :disabled="record.status === 'running'"
                      >
                        <Play class="btn-icon" />
                        运行
                      </a-button>
                      <a-button
                        size="small"
                        @click="editTask(record)"
                      >
                        <Edit class="btn-icon" />
                        编辑
                      </a-button>
                      <a-button
                        size="small"
                        @click="viewTaskHistory(record)"
                      >
                        <History class="btn-icon" />
                        历史
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 批量操作 -->
        <a-tab-pane
          key="batch"
          tab="批量操作"
        >
          <div class="batch-section">
            <div class="batch-tools">
              <div class="tool-card">
                <div class="tool-header">
                  <Layers class="tool-icon" />
                  <h3>批量设备操作</h3>
                </div>
                <p class="tool-description">对多个设备执行相同的操作命令</p>
                <a-button
                  block
                  @click="showBatchDeviceModal"
                >
                  开始批量操作
                </a-button>
              </div>

              <div class="tool-card">
                <div class="tool-header">
                  <Upload class="tool-icon" />
                  <h3>批量配置部署</h3>
                </div>
                <p class="tool-description">批量部署配置文件到多个设备</p>
                <a-button
                  block
                  @click="showBatchConfigModal"
                >
                  批量部署配置
                </a-button>
              </div>

              <div class="tool-card">
                <div class="tool-header">
                  <Download class="tool-icon" />
                  <h3>批量数据收集</h3>
                </div>
                <p class="tool-description">从多个设备收集状态和配置信息</p>
                <a-button
                  block
                  @click="showBatchCollectModal"
                >
                  批量数据收集
                </a-button>
              </div>

              <div class="tool-card">
                <div class="tool-header">
                  <RefreshCw class="tool-icon" />
                  <h3>批量重启服务</h3>
                </div>
                <p class="tool-description">批量重启指定的系统服务</p>
                <a-button
                  block
                  @click="showBatchRestartModal"
                >
                  批量重启服务
                </a-button>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- API集成 -->
        <a-tab-pane
          key="api"
          tab="API集成"
        >
          <div class="api-section">
            <div class="section-header">
              <h2 class="section-title">API集成管理</h2>
              <a-button @click="addApiIntegration">
                <Plus class="btn-icon" />
                添加API集成
              </a-button>
            </div>

            <div class="api-grid">
              <div
                v-for="api in apiIntegrations"
                :key="api.id"
                class="api-card"
                :class="api.status"
              >
                <div class="api-header">
                  <div class="api-info">
                    <component
                      :is="getApiIcon(api.type)"
                      class="api-icon"
                    />
                    <div class="api-details">
                      <h3 class="api-name">{{ api.name }}</h3>
                      <p class="api-url">{{ api.url }}</p>
                    </div>
                  </div>
                  <div
                    class="api-status"
                    :class="api.status"
                  >
                    <div class="status-dot"></div>
                    <span>{{ getApiStatusLabel(api.status) }}</span>
                  </div>
                </div>

                <div class="api-metrics">
                  <div class="metric-item">
                    <span class="metric-label">请求数</span>
                    <span class="metric-value">{{ api.requestCount }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value">{{ api.successRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">平均响应</span>
                    <span class="metric-value">{{ api.avgResponseTime }}ms</span>
                  </div>
                </div>

                <div class="api-actions">
                  <a-button
                    size="small"
                    @click="testApi(api)"
                    :loading="api.testing"
                  >
                    <Zap class="btn-icon" />
                    测试
                  </a-button>
                  <a-button
                    size="small"
                    @click="editApi(api)"
                  >
                    <Edit class="btn-icon" />
                    编辑
                  </a-button>
                  <a-button
                    size="small"
                    @click="viewApiLogs(api)"
                  >
                    <FileText class="btn-icon" />
                    日志
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 创建脚本模态框 -->
    <a-modal
      v-model:open="showCreateScriptModal"
      title="创建自动化脚本"
      width="800px"
      @ok="handleCreateScript"
      @cancel="cancelCreateScript"
    >
      <a-form
        ref="scriptFormRef"
        :model="scriptForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="脚本名称"
          name="name"
          :rules="[{ required: true, message: '请输入脚本名称' }]"
        >
          <a-input
            v-model:value="scriptForm.name"
            placeholder="请输入脚本名称"
          />
        </a-form-item>

        <a-form-item
          label="脚本描述"
          name="description"
        >
          <a-textarea
            v-model:value="scriptForm.description"
            placeholder="请输入脚本描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item
          label="脚本类型"
          name="type"
          :rules="[{ required: true, message: '请选择脚本类型' }]"
        >
          <a-select
            v-model:value="scriptForm.type"
            placeholder="请选择脚本类型"
          >
            <a-select-option value="shell">Shell脚本</a-select-option>
            <a-select-option value="python">Python脚本</a-select-option>
            <a-select-option value="powershell">PowerShell</a-select-option>
            <a-select-option value="ansible">Ansible</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="脚本内容"
          name="content"
          :rules="[{ required: true, message: '请输入脚本内容' }]"
        >
          <a-textarea
            v-model:value="scriptForm.content"
            placeholder="请输入脚本内容"
            :rows="10"
            style="font-family: 'Courier New', monospace"
          />
        </a-form-item>

        <a-form-item
          label="执行超时"
          name="timeout"
        >
          <a-input-number
            v-model:value="scriptForm.timeout"
            :min="1"
            :max="3600"
            placeholder="秒"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="标签"
          name="tags"
        >
          <a-select
            v-model:value="scriptForm.tags"
            mode="tags"
            placeholder="添加标签"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 创建任务模态框 -->
    <a-modal
      v-model:open="showCreateTaskModal"
      title="创建调度任务"
      width="700px"
      @ok="handleCreateTask"
      @cancel="cancelCreateTask"
    >
      <a-form
        ref="taskFormRef"
        :model="taskForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="任务名称"
          name="name"
          :rules="[{ required: true, message: '请输入任务名称' }]"
        >
          <a-input
            v-model:value="taskForm.name"
            placeholder="请输入任务名称"
          />
        </a-form-item>

        <a-form-item
          label="关联脚本"
          name="scriptId"
          :rules="[{ required: true, message: '请选择关联脚本' }]"
        >
          <a-select
            v-model:value="taskForm.scriptId"
            placeholder="请选择关联脚本"
          >
            <a-select-option
              v-for="script in scripts"
              :key="script.id"
              :value="script.id"
            >
              {{ script.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="调度规则"
          name="schedule"
          :rules="[{ required: true, message: '请输入调度规则' }]"
        >
          <a-input
            v-model:value="taskForm.schedule"
            placeholder="例如: 0 6 * * * (每天6点执行)"
          />
        </a-form-item>

        <a-form-item
          label="任务描述"
          name="description"
        >
          <a-textarea
            v-model:value="taskForm.description"
            placeholder="请输入任务描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item
          label="启用状态"
          name="enabled"
        >
          <a-switch
            v-model:checked="taskForm.enabled"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'
import {
  ArrowLeft,
  Bot,
  RefreshCw,
  Plus,
  Calendar,
  Layers,
  FileCode,
  Clock,
  Activity,
  Zap,
  Play,
  Edit,
  FileText,
  History,
  Upload,
  Download,
  Search
} from 'lucide-vue-next'

const router = useRouter()
const loading = ref(false)
const activeTab = ref('scripts')
const selectedScriptType = ref('all')
const selectedScriptStatus = ref('all')
const selectedTaskStatus = ref('all')

// 模态框状态
const showCreateScriptModal = ref(false)
const showCreateTaskModal = ref(false)

// 表单引用
const scriptFormRef = ref()
const taskFormRef = ref()

// 脚本表单数据
const scriptForm = reactive({
  id: null as number | null,
  name: '',
  description: '',
  type: '',
  content: '',
  timeout: 300,
  tags: []
})

// 任务表单数据
const taskForm = reactive({
  name: '',
  scriptId: null,
  schedule: '',
  description: '',
  enabled: true
})

// 自动化统计数据
const automationStats = reactive({
  totalScripts: 45,
  activeScripts: 38,
  inactiveScripts: 7,
  totalTasks: 23,
  runningTasks: 5,
  waitingTasks: 12,
  todayExecutions: 156,
  successfulExecutions: 142,
  failedExecutions: 14,
  apiIntegrations: 12,
  onlineApis: 10,
  offlineApis: 2
})

// 脚本数据
const scripts = ref([
  {
    id: 1,
    name: '系统健康检查',
    description: '检查系统CPU、内存、磁盘使用情况',
    type: 'shell',
    status: 'active',
    executionCount: 245,
    successRate: 98.5,
    lastExecution: '2024-01-15 14:30',
    executing: false
  },
  {
    id: 2,
    name: '日志清理脚本',
    description: '清理超过30天的系统日志文件',
    type: 'python',
    status: 'active',
    executionCount: 89,
    successRate: 100,
    lastExecution: '2024-01-15 02:00',
    executing: false
  },
  {
    id: 3,
    name: '备份数据库',
    description: '自动备份MySQL数据库',
    type: 'shell',
    status: 'inactive',
    executionCount: 156,
    successRate: 95.2,
    lastExecution: '2024-01-14 23:00',
    executing: false
  }
])

// 任务数据
const tasks = ref([
  {
    id: 1,
    name: '每日系统检查',
    script: '系统健康检查',
    schedule: '每天 06:00',
    status: 'running',
    nextRun: '2024-01-16 06:00',
    lastRun: '2024-01-15 06:00'
  },
  {
    id: 2,
    name: '周末日志清理',
    script: '日志清理脚本',
    schedule: '每周日 02:00',
    status: 'waiting',
    nextRun: '2024-01-21 02:00',
    lastRun: '2024-01-14 02:00'
  }
])

// API集成数据
const apiIntegrations = ref([
  {
    id: 1,
    name: 'Zabbix监控API',
    type: 'monitoring',
    url: 'https://zabbix.company.com/api',
    status: 'online',
    requestCount: 1247,
    successRate: 99.2,
    avgResponseTime: 156,
    testing: false
  },
  {
    id: 2,
    name: 'LDAP用户同步',
    type: 'authentication',
    url: 'ldap://ldap.company.com',
    status: 'offline',
    requestCount: 89,
    successRate: 85.4,
    avgResponseTime: 234,
    testing: false
  }
])

// 任务表格列定义
const taskColumns = [
  { title: '任务名称', dataIndex: 'name', key: 'name' },
  { title: '关联脚本', dataIndex: 'script', key: 'script' },
  { title: '调度规则', key: 'schedule' },
  { title: '状态', key: 'status' },
  { title: '下次运行', dataIndex: 'nextRun', key: 'nextRun' },
  { title: '操作', key: 'actions' }
]

// 计算属性
const filteredScripts = computed(() => {
  return scripts.value.filter((script) => {
    const typeMatch = selectedScriptType.value === 'all' || script.type === selectedScriptType.value
    const statusMatch = selectedScriptStatus.value === 'all' || script.status === selectedScriptStatus.value
    return typeMatch && statusMatch
  })
})

const filteredTasks = computed(() => {
  return tasks.value.filter((task) => {
    return selectedTaskStatus.value === 'all' || task.status === selectedTaskStatus.value
  })
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const createScript = () => {
  showCreateScriptModal.value = true
}

const createTask = () => {
  showCreateTaskModal.value = true
}

// 处理创建脚本
const handleCreateScript = async () => {
  try {
    await scriptFormRef.value.validate()

    // 创建新脚本对象
    const newScript = {
      id: scripts.value.length + 1,
      name: scriptForm.name,
      description: scriptForm.description,
      type: scriptForm.type,
      content: scriptForm.content,
      timeout: scriptForm.timeout,
      tags: scriptForm.tags,
      status: 'active',
      executionCount: 0,
      successRate: 100,
      lastExecution: '从未执行',
      executing: false,
      createdAt: new Date().toLocaleString('zh-CN'),
      createdBy: '当前用户'
    }

    // 添加到脚本列表
    scripts.value.push(newScript)

    // 更新统计数据
    automationStats.totalScripts++
    automationStats.activeScripts++

    // 重置表单并关闭模态框
    resetScriptForm()
    showCreateScriptModal.value = false

    // 显示成功消息
    console.log('脚本创建成功:', newScript)
  } catch (error) {
    console.error('脚本创建失败:', error)
  }
}

// 处理创建任务
const handleCreateTask = async () => {
  try {
    await taskFormRef.value.validate()

    // 查找关联的脚本
    const associatedScript = scripts.value.find((s) => s.id === taskForm.scriptId)

    // 创建新任务对象
    const newTask = {
      id: tasks.value.length + 1,
      name: taskForm.name,
      script: associatedScript?.name || '未知脚本',
      scriptId: taskForm.scriptId,
      schedule: taskForm.schedule,
      description: taskForm.description,
      status: taskForm.enabled ? 'waiting' : 'disabled',
      nextRun: calculateNextRun(taskForm.schedule),
      lastRun: '从未运行',
      createdAt: new Date().toLocaleString('zh-CN'),
      createdBy: '当前用户'
    }

    // 添加到任务列表
    tasks.value.push(newTask)

    // 更新统计数据
    automationStats.totalTasks++
    if (taskForm.enabled) {
      automationStats.waitingTasks++
    }

    // 重置表单并关闭模态框
    resetTaskForm()
    showCreateTaskModal.value = false

    // 显示成功消息
    console.log('任务创建成功:', newTask)
  } catch (error) {
    console.error('任务创建失败:', error)
  }
}

// 取消创建脚本
const cancelCreateScript = () => {
  resetScriptForm()
  showCreateScriptModal.value = false
}

// 取消创建任务
const cancelCreateTask = () => {
  resetTaskForm()
  showCreateTaskModal.value = false
}

// 重置脚本表单
const resetScriptForm = () => {
  Object.assign(scriptForm, {
    name: '',
    description: '',
    type: '',
    content: '',
    timeout: 300,
    tags: []
  })
}

// 重置任务表单
const resetTaskForm = () => {
  Object.assign(taskForm, {
    name: '',
    scriptId: null,
    schedule: '',
    description: '',
    enabled: true
  })
}

// 计算下次运行时间（简化实现）
const calculateNextRun = (schedule: string) => {
  // 这里应该根据cron表达式计算下次运行时间
  // 简化实现，返回一个小时后的时间
  const nextRun = new Date()
  nextRun.setHours(nextRun.getHours() + 1)
  return nextRun.toLocaleString('zh-CN')
}

const showBatchOperations = () => {
  activeTab.value = 'batch'
}

const executeScript = (script: any) => {
  script.executing = true
  setTimeout(() => {
    script.executing = false
    script.executionCount++
    script.lastExecution = new Date().toLocaleString('zh-CN')
  }, 2000)
}

const editScript = (script: any) => {
  // 填充表单数据
  Object.assign(scriptForm, {
    name: script.name,
    description: script.description,
    type: script.type,
    content: script.content || '',
    timeout: script.timeout || 300,
    tags: script.tags || []
  })

  // 设置编辑模式
  scriptForm.id = script.id
  showCreateScriptModal.value = true
}

const viewScriptLogs = (script: any) => {
  // 这里应该打开日志查看器
  // 简化实现，显示模拟日志
  const logs = [
    `[${new Date().toLocaleString()}] 脚本 "${script.name}" 开始执行`,
    `[${new Date().toLocaleString()}] 执行环境: ${script.type}`,
    `[${new Date().toLocaleString()}] 脚本执行成功`,
    `[${new Date().toLocaleString()}] 执行耗时: 2.3秒`
  ]

  console.log('脚本执行日志:', logs)
  // 这里可以打开一个日志查看模态框
}

const runTask = (task: any) => {
  console.log('运行任务:', task)
}

const editTask = (task: any) => {
  console.log('编辑任务:', task)
}

const viewTaskHistory = (task: any) => {
  console.log('查看任务历史:', task)
}

const showBatchDeviceModal = () => {
  // 实现批量设备操作
  const selectedDevices = ['************', '************', '************']
  const command = 'systemctl status nginx'

  console.log('开始批量设备操作:', {
    devices: selectedDevices,
    command: command,
    timestamp: new Date().toLocaleString()
  })

  // 模拟批量执行
  selectedDevices.forEach((device, index) => {
    setTimeout(
      () => {
        console.log(`设备 ${device} 执行完成: ${command}`)
      },
      (index + 1) * 1000
    )
  })
}

const showBatchConfigModal = () => {
  // 实现批量配置部署
  const configFile = '/etc/nginx/nginx.conf'
  const targetDevices = ['web-server-01', 'web-server-02', 'web-server-03']

  console.log('开始批量配置部署:', {
    configFile: configFile,
    targets: targetDevices,
    timestamp: new Date().toLocaleString()
  })

  // 模拟批量部署
  targetDevices.forEach((device, index) => {
    setTimeout(
      () => {
        console.log(`配置已部署到 ${device}: ${configFile}`)
      },
      (index + 1) * 800
    )
  })
}

const showBatchCollectModal = () => {
  // 实现批量数据收集
  const collectCommands = ['df -h', 'free -m', 'ps aux | head -10', 'netstat -tuln']
  const targetServers = ['server-01', 'server-02', 'server-03']

  console.log('开始批量数据收集:', {
    commands: collectCommands,
    servers: targetServers,
    timestamp: new Date().toLocaleString()
  })

  // 模拟数据收集
  targetServers.forEach((server, serverIndex) => {
    collectCommands.forEach((cmd, cmdIndex) => {
      setTimeout(
        () => {
          console.log(`从 ${server} 收集数据: ${cmd}`)
        },
        (serverIndex * collectCommands.length + cmdIndex + 1) * 500
      )
    })
  })
}

const showBatchRestartModal = () => {
  // 实现批量重启服务
  const services = ['nginx', 'mysql', 'redis']
  const targetServers = ['prod-server-01', 'prod-server-02']

  console.log('开始批量重启服务:', {
    services: services,
    servers: targetServers,
    timestamp: new Date().toLocaleString()
  })

  // 模拟批量重启
  targetServers.forEach((server, serverIndex) => {
    services.forEach((service, serviceIndex) => {
      setTimeout(
        () => {
          console.log(`在 ${server} 上重启服务: ${service}`)
        },
        (serverIndex * services.length + serviceIndex + 1) * 1200
      )
    })
  })
}

const addApiIntegration = () => {
  console.log('添加API集成')
}

const testApi = (api: any) => {
  api.testing = true
  setTimeout(() => {
    api.testing = false
  }, 1500)
}

const editApi = (api: any) => {
  console.log('编辑API:', api)
}

const viewApiLogs = (api: any) => {
  console.log('查看API日志:', api)
}

const getScriptIcon = (type: string) => {
  const icons: Record<string, any> = {
    shell: FileCode,
    python: FileCode,
    powershell: FileCode,
    ansible: FileCode
  }
  return icons[type] || FileCode
}

const getScriptStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '活跃',
    inactive: '停用'
  }
  return labels[status] || status
}

const getTaskStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    running: 'blue',
    waiting: 'orange',
    completed: 'green',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getTaskStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    running: '运行中',
    waiting: '等待中',
    completed: '已完成',
    failed: '失败'
  }
  return labels[status] || status
}

const getApiIcon = (type: string) => {
  const icons: Record<string, any> = {
    monitoring: Activity,
    authentication: Zap,
    database: FileCode,
    notification: FileCode
  }
  return icons[type] || Zap
}

const getApiStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线'
  }
  return labels[status] || status
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.automation-manager {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.overview-stats {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-icon {
  width: 16px;
  height: 16px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
}

.stat-details {
  display: flex;
  gap: 16px;
}

.stat-detail {
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #666;
}

.detail-value {
  font-size: 12px;
  font-weight: 600;
}

.detail-value.active {
  color: #52c41a;
}

.detail-value.inactive {
  color: #ff4d4f;
}

.detail-value.running {
  color: #1890ff;
}

.detail-value.waiting {
  color: #fa8c16;
}

.detail-value.success {
  color: #52c41a;
}

.detail-value.failed {
  color: #ff4d4f;
}

.detail-value.online {
  color: #52c41a;
}

.detail-value.offline {
  color: #ff4d4f;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.scripts-section,
.tasks-section,
.batch-section,
.api-section {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
  font-weight: 600;
}

.section-filters {
  display: flex;
  gap: 12px;
}

.scripts-grid,
.api-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.script-card,
.api-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.script-card:hover,
.api-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.script-card.active,
.api-card.online {
  border-left: 4px solid #52c41a;
}

.script-card.inactive,
.api-card.offline {
  border-left: 4px solid #ff4d4f;
}

.script-header,
.api-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.script-info,
.api-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.script-icon,
.api-icon {
  width: 32px;
  height: 32px;
  color: #1890ff;
  flex-shrink: 0;
}

.script-details,
.api-details {
  flex: 1;
}

.script-name,
.api-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
  font-weight: 600;
}

.script-description {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.api-url {
  margin: 0;
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.script-status,
.api-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.script-status.active,
.api-status.online {
  color: #52c41a;
}

.script-status.inactive,
.api-status.offline {
  color: #ff4d4f;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.script-metrics,
.api-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.metric-label {
  font-size: 10px;
  color: #666;
}

.metric-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

.script-actions,
.api-actions {
  display: flex;
  gap: 8px;
}

.tasks-table {
  background: white;
}

.schedule-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.schedule-icon {
  width: 14px;
  height: 14px;
  color: #666;
}

.batch-tools {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.tool-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.tool-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.tool-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.tool-icon {
  width: 48px;
  height: 48px;
  color: #1890ff;
}

.tool-header h3 {
  margin: 0;
  font-size: 16px;
  color: #1a1a1a;
}

.tool-description {
  margin: 0 0 16px 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 16px;
  height: 16px;
}
</style>
