/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AIAssistantPanel: typeof import('./src/components/AIAssistantPanel.vue')['default']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpace: typeof import('ant-design-vue/es')['Space']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimeline: typeof import('ant-design-vue/es')['Timeline']
    ATimelineItem: typeof import('ant-design-vue/es')['TimelineItem']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    AWatermark: typeof import('ant-design-vue/es')['Watermark']
    CloudIntegrationPanel: typeof import('./src/components/CloudIntegrationPanel.vue')['default']
    CollaborationPanel: typeof import('./src/components/CollaborationPanel.vue')['default']
    CommandDialog: typeof import('./src/components/global/CommandDialog.vue')['default']
    ConnectionConfig: typeof import('./src/components/connections/ConnectionConfig.vue')['default']
    ConsoleConnector: typeof import('./src/components/connections/ConsoleConnector.vue')['default']
    DataAnalyticsPanel: typeof import('./src/components/DataAnalyticsPanel.vue')['default']
    EnhancedCommandHistory: typeof import('./src/components/EnhancedCommandHistory.vue')['default']
    MfaDialog: typeof import('./src/components/global/mfa/MfaDialog.vue')['default']
    OtpInput: typeof import('./src/components/global/mfa/OtpInput.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PluginManager: typeof import('./src/components/PluginManager.vue')['default']
    RdpConnector: typeof import('./src/components/connections/RdpConnector.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
